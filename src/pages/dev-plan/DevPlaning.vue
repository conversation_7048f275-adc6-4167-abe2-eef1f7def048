<template>
  <q-page padding>
    <div v-if="loading" class="text-center">
      <q-spinner size="lg" />
      <div class="q-mt-sm">กำลังโหลดข้อมูล...</div>
    </div>
    <div v-else-if="developmentPlan">
      <div class="text-h5 q-mb-md text-weight-bold">
        จัดการแผนพัฒนาบุคลากร ปี {{ globalStore.developmentPlanYear }}
      </div>
      <div class="text-body1 q-mb-md text-weight-bold">
        แผนพัฒนาบุคลากร ปี {{ globalStore.developmentPlanYear }}
      </div>
      <div class="q-mb-md row items-center">
        <span class="q-mr-sm">สถานะ :</span>
        <StatusCapsule :published="developmentPlan.isActive" class="q-mr-sm" />
        <q-btn class="q-ml-sm bg-black text-white">เผยแพร่</q-btn>
      </div>
      <div class="q-mb-md row items-center">
        <span class="q-mr-sm">ค้นหาตาม :</span>
        <SearchDropDownBar placeholder="หน่วยงาน/ส่วนงาน" :options="searchOptions" />
      </div>
      <div v-if="selectedTab === 'position'" class="q-mb-md row items-center">
        <span class="q-mr-sm">ค้นหาตาม :</span>
        <SearchDropDownBar placeholder="ประเภทสายงาน" :options="searchTypeOptions" />
        <SearchDropDownBar placeholder="ตำแหน่ง" :options="searchPositionOptions" />
        <SearchDropDownBar placeholder="ระดับ" :options="searchRankOptions" />
      </div>
      <div class="q-mb-md">
        <TabNavigation
          v-model="selectedTab"
          :tabs="dynamicTabs.map((tab) => ({ label: tab.label, value: tab.name }))"
          class="q-mb-md"
        />
      </div>
    </div>
    <div v-else class="text-center text-grey-6">ไม่พบข้อมูลแผนพัฒนา</div>

    <div v-if="!loading && developmentPlan">
      <!-- Loading state for type plans -->
      <div v-if="isLoadingTypePlans" class="text-center q-pa-md">
        <q-spinner size="md" />
        <div class="q-mt-sm">กำลังโหลดข้อมูลประเภทแผน...</div>
      </div>

      <!-- Dynamic tab content -->
      <div v-else-if="currentTabData.length > 0" class="q-mb-md">
        <div class="row items-center justify-between q-mb-md">
          <span class="text-weight-bold">
            {{ dynamicTabs.find((tab) => tab.name === selectedTab)?.label || 'ข้อมูลแผน' }}
          </span>
          <q-btn label="เพิ่ม" color="accent" icon="add" dense size="sm" class="q-pr-sm" />
        </div>

        <!-- Display items for current tab -->
        <div v-for="(item, index) in currentTabData" :key="item.id" class="q-mb-sm">
          <q-card class="q-pa-md">
            <div class="row items-center justify-between">
              <div class="col">
                <div class="text-weight-bold">{{ item.name }}</div>
                <div v-if="item.description" class="text-grey-7 q-mt-xs">
                  {{ item.description }}
                </div>
                <div class="q-mt-xs">
                  <template v-for="(value, key) in item" :key="key">
                    <q-chip
                      v-if="key !== 'id' && key !== 'name' && key !== 'description' && value"
                      size="sm"
                      color="primary"
                      text-color="white"
                    >
                      {{ key }}: {{ value }}
                    </q-chip>
                  </template>
                </div>
              </div>
              <div class="col-auto">
                <q-btn
                  icon="delete"
                  color="negative"
                  flat
                  round
                  size="sm"
                  @click="removeTypePlanItem(index)"
                />
              </div>
            </div>
          </q-card>
        </div>
      </div>

      <!-- Empty state -->
      <div
        v-else-if="!isLoadingTypePlans && dynamicTabs.length === 0"
        class="text-center q-pa-md text-grey-6"
      >
        ไม่พบข้อมูลประเภทแผน
      </div>

      <!-- No data for selected tab -->
      <div
        v-else-if="!isLoadingTypePlans && currentTabData.length === 0"
        class="text-center q-pa-md text-grey-6"
      >
        ไม่มีข้อมูลในแท็บนี้
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { useQuasar } from 'quasar';
import StatusCapsule from 'src/components/common/StatusCapsule.vue';
import { useRoute } from 'vue-router';
import type { DevelopmentPlan } from 'src/types/idp';
import { useGlobalStore } from 'src/stores/global';
import SearchDropDownBar from 'src/components/SearchDropDownBar.vue';
import { api as axios } from 'src/boot/axios';

import TabNavigation from 'src/components/common/TabNavigation.vue';

// Type definition for type-plans API response
interface TypePlanItem {
  id: number;
  name: string;
  description?: string;
  [key: string]: unknown; // Allow for additional properties
}

interface GroupedTypePlans {
  [key: string]: TypePlanItem[];
}

// Mock options for SearchDropDownBar
const searchOptions = [
  'กองบริหารและพัฒนาทรัพยากรบุคคล',
  'สํานักคอมพิวเตอร์',
  'กองแผนงาน',
  'กองกิจการนิสิต',
];

const searchTypeOptions = ['ทุกสายงาน', 'สายวิชาการ', 'สายสนับสนุนวิชาการ'];

const searchPositionOptions = [
  'ทุกตําแหน่ง',
  'นักวิชาการเงินและบัญชี',
  'นักวิชาการศึกษา',
  'นักวิชาการคอมพิวเตอร์',
  'นักวิชาการพัสดุ',
];

const searchRankOptions = ['ปฏิบัติการ', 'ชํานาญการ', 'ชํานาญการพิเศษ', 'ทรงคุณวุฒิ'];

// Reactive data for type plans
const typePlansData = ref<TypePlanItem[]>([]);
const groupedTypePlans = ref<GroupedTypePlans>({});
const dynamicTabs = ref<Array<{ name: string; label: string }>>([]);
const selectedTab = ref('');
const isLoadingTypePlans = ref(false);

// Function to fetch type plans from API
const fetchTypePlans = async () => {
  isLoadingTypePlans.value = true;
  try {
    const response = await axios.get('http://localhost:3000/type-plans');
    typePlansData.value = response.data;

    // Group data by name field
    const grouped: GroupedTypePlans = {};
    response.data.forEach((item: TypePlanItem) => {
      if (!grouped[item.name]) {
        grouped[item.name] = [];
      }
      grouped[item.name]?.push(item);
    });

    groupedTypePlans.value = grouped;

    // Create dynamic tabs based on unique names
    const uniqueNames = Object.keys(grouped);
    dynamicTabs.value = uniqueNames.map((name) => ({
      name: name.toLowerCase().replace(/\s+/g, '_'), // Convert to safe tab name
      label: name,
    }));

    // Set first tab as selected if available
    if (dynamicTabs.value.length > 0) {
      selectedTab.value = dynamicTabs.value[0]?.name || '';
    }
  } catch (error) {
    console.error('Error fetching type plans:', error);
    // Fallback to empty state
    typePlansData.value = [];
    groupedTypePlans.value = {};
    dynamicTabs.value = [];
  } finally {
    isLoadingTypePlans.value = false;
  }
};

// Function to remove a type plan item
const removeTypePlanItem = (index: number) => {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: 'คุณต้องการลบรายการนี้หรือไม่?',
    cancel: true,
    persistent: true,
  }).onOk(() => {
    const currentTabLabel = dynamicTabs.value.find((tab) => tab.name === selectedTab.value)?.label;
    if (currentTabLabel && groupedTypePlans.value[currentTabLabel]) {
      groupedTypePlans.value[currentTabLabel]?.splice(index, 1);
    }
  });
};

// Computed property to get current tab data
const currentTabData = computed(() => {
  const currentTabLabel = dynamicTabs.value.find((tab) => tab.name === selectedTab.value)?.label;
  return currentTabLabel ? groupedTypePlans.value[currentTabLabel] || [] : [];
});

// Quasar instance for dialogs

const $q = useQuasar();

// ตัวอย่าง mock tab name ที่รองรับ overview, plan, result
// devPlanTabs ในที่นี้ควรมี name: 'overview', 'plan', 'result' อย่างน้อย 1 อัน

const route = useRoute();
const globalStore = useGlobalStore();
const loading = ref(false);
const developmentPlan = ref<DevelopmentPlan | null>(null);

// Mock data - same as in DevPlanManagement.vue
const mockCompetencies: DevelopmentPlan[] = [
  {
    id: 1,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2567',
    description: 'ความสามารถในการวางแผน ดำเนินการ และควบคุมโครงการให้สำเร็จตามเป้าหมาย',
    isActive: true,
    originalPlan: null,
  },
  {
    id: 2,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2568',
    description: 'ทักษะในการสื่อสาร การนำเสนอข้อมูล และการประสานงานกับผู้อื่น',
    isActive: true,
    originalPlan: null,
  },
  {
    id: 3,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2569',
    description: 'ความสามารถในการวิเคราะห์ปัญหา ประมวลผลข้อมูล และหาแนวทางแก้ไข',
    isActive: true,
    originalPlan: null,
  },
  {
    id: 4,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2570',
    description: 'ความสามารถในการเป็นผู้นำ การมอบหมายงาน และการสร้างแรงบันดาลใจให้ทีม',
    isActive: false,
    originalPlan: null,
  },
  {
    id: 5,
    originalPlanId: null,
    name: 'แผนพัฒนาศักยภาพผู้ปฏิบัติงานในมหาวิทยาลัย ปี 2571',
    description: 'ทักษะการใช้เทคโนโลยี การใช้งานซอฟต์แวร์ และการปรับตัวกับเทคโนโลยีใหม่',
    isActive: false,
    originalPlan: null,
  },
];

// Function to extract year from plan name
const extractYearFromPlanName = (planName: string): string => {
  const yearMatch = planName.match(/ปี\s*(\d{4})/);
  return yearMatch?.[1] ?? 'XXXX';
};

// Function to update global store with dynamic breadcrumb
const updateDevelopmentPlanBreadcrumb = (plan: DevelopmentPlan | null) => {
  if (plan) {
    const year = extractYearFromPlanName(plan.name);
    globalStore.setDevelopmentPlanYear(year);
  }
};

// Function to fetch development plan by ID
const fetchDevelopmentPlan = (id: number) => {
  loading.value = true;
  try {
    // In a real application, this would be an API call
    // For now, using mock data
    const plan = mockCompetencies.find((p) => p.id === id);
    developmentPlan.value = plan || null;
    updateDevelopmentPlanBreadcrumb(plan || null);
  } catch (error) {
    console.error('Error fetching development plan:', error);
    developmentPlan.value = null;
    updateDevelopmentPlanBreadcrumb(null);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  const planId = Number(route.params.id);
  if (planId && !isNaN(planId)) {
    fetchDevelopmentPlan(planId);
  }
  // Fetch type plans data when component mounts
  void fetchTypePlans();
});

// Watch for route parameter changes
watch(
  () => route.params.id,
  (newId) => {
    const planId = Number(newId);
    if (planId && !isNaN(planId)) {
      fetchDevelopmentPlan(planId);
    }
  },
);

// Clear breadcrumb when component unmounts
onUnmounted(() => {
  globalStore.clearDevelopmentPlanYear();
});
</script>

<style scoped></style>
