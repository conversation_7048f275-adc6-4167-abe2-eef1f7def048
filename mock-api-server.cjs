const express = require('express');
const cors = require('cors');
const app = express();
const port = 3001;

app.use(cors());
app.use(express.json());

const typePlansData = [
  {
    id: 1,
    name: 'General Skills',
    description: 'Basic skills',
    category: 'GENERAL',
    level: 'BEGINNER',
  },
  {
    id: 2,
    name: 'General Skills',
    description: 'Intermediate skills',
    category: 'GENERAL',
    level: 'INTERMEDIATE',
  },
  {
    id: 3,
    name: 'Leadership Skills',
    description: 'Management skills',
    category: 'LEADERSHIP',
    level: 'ADVANCED',
  },
  {
    id: 4,
    name: 'Technical Skills',
    description: 'Technical knowledge',
    category: 'TECHNICAL',
    level: 'INTERMEDIATE',
  },
];

app.get('/type-plans', (req, res) => {
  console.log('GET /type-plans - Returning mock data');
  res.json(typePlansData);
});

app.listen(port, () => {
  console.log(`Mock API server running at http://localhost:${port}`);
});
